package com.digiwin.escloud.aioitms.exam.service.impl;

import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.exam.dao.AiopsExamMapper;
import com.digiwin.escloud.aioitms.exam.dao.AiopsExamRecordMapper;
import com.digiwin.escloud.aioitms.exam.model.*;
import com.digiwin.escloud.aioitms.exam.model.enums.AiopsExamStatus;
import com.digiwin.escloud.aioitms.exam.model.params.AiopsExamItemInstanceScoreParam;
import com.digiwin.escloud.aioitms.exam.model.params.AiopsExamRecordParam;
import com.digiwin.escloud.aioitms.exam.service.AiopsExamService;
import com.digiwin.escloud.aioitms.exam.service.handler.AiopsExamRecordScoreAbstractHandler;
import com.digiwin.escloud.aioitms.exam.service.handler.AiopsExamScoreAbstractHandler;
import com.digiwin.escloud.aioitms.exam.service.handler.impl.*;
import com.digiwin.escloud.aioitms.networksecurity.model.emnus.NetworkReportType;
import com.digiwin.escloud.aioitms.networksecurity.service.NetworkSecurityExamService;
import com.digiwin.escloud.aioitms.networksecurity.service.NetworkSecurityExamSimpleEsReport;
import com.digiwin.escloud.aioitms.report.model.ReportStatus;
import com.digiwin.escloud.aioitms.report.service.serviceReprot.NetworkSecurityExamEsReport;
import com.digiwin.escloud.aioitms.util.RestDmpUtil;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aioitms.exam.model.enums.AiopsExamStatus.*;
import static com.digiwin.escloud.common.model.ResponseCode.*;

@Service
@Slf4j
public class AiopsExamServiceImpl implements AiopsExamService , ParamCheckHelp {

    private static final int SCALE = 0;
    @Resource
    private AiopsExamMapper aiopsExamMapper;

    @Resource
    private AiopsExamRecordMapper aiopsExamRecordMapper;

    @Resource
    RestDmpUtil restDmpUtil;

    @Autowired
    BigDataUtil bigDataUtil;

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private NetworkSecurityExamService networkSecurityExamService;

    @Resource
    private NetworkSecurityExamEsReport networkSecurityExamEsReport;

    @Resource
    private NetworkSecurityExamSimpleEsReport networkSecurityExamSimpleEsReport;

    @Override
    public ResponseBase<List<AiopsExam>> getAiopsExam(Long id, String code, String name, boolean needPaging, int pageNum, int pageSize) {
        List<AiopsExam> aiopsExamList = aiopsExamMapper.selectAiopsExam(id, code, name);
        if (CollectionUtils.isEmpty(aiopsExamList)) {
            return ResponseBase.ok();
        }
        List<Long> aeIdList = aiopsExamList.stream().map(AiopsExam::getId).collect(Collectors.toList());
        // 组装数据 拼接体检项目类型
        List<AiopsExamItemType> aeitList = aiopsExamMapper.selectAiopsExamItemTypeByAeId(aeIdList);
        // 组装数据 拼接运维项目和指标
        List<AiopsExamItemMap> aiopsExamItemMapList = aiopsExamMapper.selectAiopsExamItemIndexByAeId(aeIdList);

        // 查询体检指标健康状况
        List<AiopsExamLevelSetting> aelsList = aiopsExamMapper.selectAiopsExamLevel();
        Map<Long, List<AiopsExamItemMap>> aeMap = aiopsExamItemMapList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeId));
        for (AiopsExam aiopsExam : aiopsExamList) {
            List<AiopsExamItemMap> aeimList = aeMap.getOrDefault(aiopsExam.getId(),new ArrayList<>());
            Map<Long, List<AiopsExamItemMap>> aeitMap = aeimList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeitId));
            aeitList.forEach(i -> i.setAeimList(aeitMap.getOrDefault(i.getId(),new ArrayList<>())));
            buildHierarchy(aeitList);
            aiopsExam.setAeitList(aeitList);
            aiopsExam.setAelsList(aelsList);
            // 后端计算分数使用
            aiopsExam.setAeimList(aiopsExamItemMapList);
        }

        return ResponseBase.okT(aiopsExamList);
    }

    @Override
    public ResponseBase getAiopsExamRecord(AiopsExamRecordParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        param.setEid(LongUtil.isEmpty(param.getEid()) ? RequestUtil.getHeaderEid() : param.getEid());
        param.setSid(RequestUtil.getHeaderSid());

        List<AiopsExamRecord> aerList = aiopsExamMapper.selectAiopsExamRecord(param);

        if (aerList.isEmpty()) {
            return ResponseBase.ok(aerList);
        }

        List<Long> aerIdList = aerList.stream()
                .map(AiopsExamRecord::getId)
                .collect(Collectors.toList());

        List<AiopsExamItemInstanceScore> aeiisList = aiopsExamMapper.selectAiopsExamRecordInstance(aerIdList);
        // 查询运维项目排序字段
        List<AiopsExamItemMap> aeimList = aiopsExamMapper.selectAiopsExamItemIndexByAeId(Lists.newArrayList(param.getAeId()));
        Map<String, Long> aiopsItemMap = aeimList.stream().collect(Collectors.toMap(AiopsExamItemMap::getAiopsItem, AiopsExamItemMap::getId, (o, n) -> o));

        List<Long> aiIdList = aeiisList.stream().filter(aeiis -> Objects.nonNull(aeiis.getAiopsDevice())).map(aeiis -> aeiis.getAiopsDevice().getAiId()).collect(Collectors.toList());

        // 设置每个 AiopsExamRecord 对应的 AiopsExamItemInstanceScore 列表
        Map<Long, String> lastCheckTimeMap = getLastCheckTime(aiIdList);
        // 设置是否在线字段
        Map<Long, List<AiopsExamItemInstanceScore>> aeiisMap = aeiisList.stream()
                .filter(aeiis -> Objects.nonNull(aeiis.getAiopsDevice()))
                .peek(aeiis -> {
                    aeiis.setAeimId( aiopsItemMap.getOrDefault(aeiis.getAiopsItem(), 9999L));

                    CommonDeviceData aiopsDevice = aeiis.getAiopsDevice();

                    if (StringUtils.isEmpty(aiopsDevice.getLastCheckInTime()) &&
                            Objects.nonNull(lastCheckTimeMap.get(aiopsDevice.getAiId()))) {

                        aiopsDevice.setLastCheckInTime(lastCheckTimeMap.get(aiopsDevice.getAiId()));
                    }
                })
                .sorted(Comparator.comparing(AiopsExamItemInstanceScore::getAeimId))
                .collect(Collectors.groupingBy(AiopsExamItemInstanceScore::getAerId));
        // 设置每个记录的实例列表
        aerList.forEach(aer -> {

            aer.setAeiisList(aeiisMap.getOrDefault(aer.getId(), new ArrayList<>()));
            aer.setExamScore(Objects.nonNull(aer.getExamScore())?aer.getExamScore().setScale(SCALE, RoundingMode.HALF_UP):
                    null);

            List<AiopsExamRecord.AiopsItemInstanceData> aiidList = aer.getAeiisList()
                    .stream().collect(Collectors.groupingBy(AiopsExamItemInstanceScore::getAiopsItem))
                    .entrySet().stream().map(en -> {
                        AiopsExamRecord.AiopsItemInstanceData aiopsItemInstanceData = new AiopsExamRecord.AiopsItemInstanceData();
                        aiopsItemInstanceData.setAeimId(aiopsItemMap.getOrDefault(en.getKey(), 9999L));
                        aiopsItemInstanceData.setAiopsItem(en.getKey());
                        aiopsItemInstanceData.setAeiisList(en.getValue());
                        return aiopsItemInstanceData;
                    }).sorted(Comparator.comparing(AiopsExamRecord.AiopsItemInstanceData::getAeimId)).collect(Collectors.toList());
            aer.setAiidList(aiidList);
        });

        return ResponseBase.ok(new PageInfo<>(aerList));
    }


    private void buildHierarchy(List<AiopsExamItemType> itemList) {
        Map<Long, AiopsExamItemType> itemMap = new HashMap<>();
        for (AiopsExamItemType item : itemList) {
            itemMap.put(item.getId(), item);
        }

        for (AiopsExamItemType item : itemList) {
            item.setChildren(new ArrayList<>());
        }

        List<AiopsExamItemType> rootItems = new ArrayList<>();
        for (AiopsExamItemType item : itemList) {
            if (Objects.nonNull(item.getParentId())) {
                AiopsExamItemType parent = itemMap.get(item.getParentId());
                if (parent != null) {
                    parent.getChildren().add(item);
                }
            } else {
                rootItems.add(item);
            }
        }

        itemList.clear();
        itemList.addAll(rootItems);
    }


    private Map<Long, String> getLastCheckTime(List<Long> list) {
        StringBuilder sf = new StringBuilder();
        //3天内才算在线
        String aiIds = list.stream().map(String::valueOf).collect(Collectors.joining(","));

        sf.append("SELECT a.aiId,DATE_FORMAT(FROM_UNIXTIME(a.flumeTimestamp / 1000), '%Y-%m-%d %H:%i:%s') AS formatted_date_time FROM servicecloud.OfflineCheckCollected_sr_primary a ");
        sf.append(" where a.aiId in (").append(aiIds).append(")");
        sf.append(" order by a.collectedTime desc");
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sf.toString());
        return dataList
                .stream()
                .filter(i -> i.get("formatted_date_time") != null)
                .filter(i -> i.get("aiId") != null)
                .collect(Collectors.toMap(key -> LongUtil.objectToLong(key.get("aiId").toString()), value -> value.get("formatted_date_time").toString(), (o, n) -> o));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase saveAiopsExamRecord(AiopsExamRecord record) {
        record.setId(SnowFlake.getInstance().newId());
        record.setSid(RequestUtil.getHeaderSid());
        record.setExamStartTime(LocalDateTime.now());
        record.setExamStatus(PAUSED);
        aiopsExamRecordMapper.insertAiopsExamRecord(record);
        
        // 根据aerId和aiopsItemId去重
        List<AiopsExamItemInstanceScore> uniqueScores = record.getAeiisList().stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                score -> score.getAerId() + "_" + score.getAiopsItemId(),
                                Function.identity(),
                                (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())
                ));
        
        for (AiopsExamItemInstanceScore score : uniqueScores) {
            saveAiopsExamItemInstanceScore(score, record.getId());
        }

        return ResponseBase.ok(record.getId());
    }

    @Override
    public ResponseBase updateAiopsExamRecordStatus(Long id, AiopsExamStatus status,boolean needCompute) {
        // 更新体检记录状态
        aiopsExamRecordMapper.updateAiopsExamRecordStatus(id, status, Lists.newArrayList(EXAM_ING, PAUSED, NOT_START));
        // 如果状态为 EXAM_COMPLETE，则进行额外的处理
        if (needCompute && EXAM_COMPLETE.equals(status)) {
            ResponseBase<AiopsExamRecord> aerRb = getAerById(id);
            if (!aerRb.checkIsSuccess()) {
                return aerRb;
            }
            AiopsExamRecord aer = aerRb.getData();
            if (!EXAM_COMPLETE.equals(aer.getExamStatus())) {
                return ResponseBase.error(AIOPS_EXAM_STATUS_ERROR);
            }

            // 获取体检信息
            AiopsExam ae = getAeById(aer.getAeId());
            if (Objects.isNull(ae)) {
                return ResponseBase.error(AIOPS_EXAM_ERROR);
            }

            // 获取体检级别设置列表
            List<AiopsExamLevelSetting> aelsList = ae.getAelsList();

            // 查询所有的指标类型
            List<AiopsExamIndexType> aeitList = aiopsExamMapper.selectIndexTypeByAeid(aer.getAeId());

            // 查询记录对应的实例分数相关数据
            List<AiopsExamItemInstanceScore> aeiisList = aiopsExamRecordMapper.selectAiopsExamItemInstanceScore(aer.getId());

            // 创建处理链
            AiopsExamRecordScoreAbstractHandler recordScoreItemTypeAbstractHandler = new AiopsExamItemTypeScoreHandler();
            AiopsExamRecordScoreAbstractHandler itemAndIndexTypeScoreHandler = new AiopsExamItemAndIndexTypeScoreHandler();
            AiopsExamRecordScoreAbstractHandler indexScoreHandler = new AiopsExamIndexScoreHandler();
            AiopsExamRecordScoreAbstractHandler recordHandler = new AiopsExamRecordHandler();
            recordScoreItemTypeAbstractHandler.setSuccessor(itemAndIndexTypeScoreHandler);
            itemAndIndexTypeScoreHandler.setSuccessor(indexScoreHandler);
            indexScoreHandler.setSuccessor(recordHandler);

            // 创建上下文并处理评分
            RecordScoreContext scoreContext = new RecordScoreContext(aeiisList, id, aelsList, aiopsExamRecordMapper, aeitList);
            recordScoreItemTypeAbstractHandler.handleScore(scoreContext);
        }

        return ResponseBase.ok();
    }
    //
    @Override
    public ResponseBase computeScore(Long aeId, Long aerId, String aiopsItemId,Long netWorkSecurityExamAssetId) {
        AiopsExam ae = getAeById(aeId);

        if (Objects.isNull(ae)) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }
        // 把体检记录暂停修改为开始
        // 更新体检记录状态
        AiopsExamItemInstanceScore aeiis = aiopsExamRecordMapper.selectAiopsExamItemInstance(aerId, aiopsItemId,netWorkSecurityExamAssetId);
        if (Objects.isNull(aeiis)) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }
        List<String> aiopsItemList = aiopsExamRecordMapper.selectAiopsExamItem(aerId).stream().map(AiopsExamItemMap::getAiopsItem)
                .collect(Collectors.toList());

        if (aeiis.getExamComplete()) {
            return ResponseBase.ok();
        }

        List<AiopsExamLevelSetting> aelsList = ae.getAelsList();
        ResponseBase<AiopsExamRecord> aerRb = getAerById(aerId);
        if (!aerRb.checkIsSuccess()) {
            return aerRb;
        }
        if (!PAUSED.equals(aerRb.getData().getExamStatus()) &&
                !EXAM_ING.equals(aerRb.getData().getExamStatus()) &&
                !NOT_START.equals(aerRb.getData().getExamStatus())) {
            return ResponseBase.error(AIOPS_EXAM_STATUS_ERROR);
        }

        // 查询该体检的开启的运维项
        // 查询该体检开启运维项的所有指标
        List<AiopsExamItemMap> aeimList = ae.getAeimList().stream()
                .filter(aeim -> aiopsItemList.contains(aeim.getAiopsItem())).collect(Collectors.toList());


//        if (aeimList.stream().anyMatch(i -> CollectionUtils.isEmpty(i.getAeiList()))) {
//            List<AiopsExamItemMap> emptyIndex = ae.getAeimList().stream().filter(i -> CollectionUtils.isEmpty(i.getAeiList())).collect(Collectors.toList());
//            return ResponseBase.error(AIOPS_EXAM_ITEM_INDEX_IS_EMPTY, emptyIndex);
//        }

        // 查询所有的指标类型
        List<AiopsExamIndexType> aeitList = aiopsExamMapper.selectIndexTypeByAeid(aeId);
        // 查询标签元数据
        Set<Long> allTagIds = aeimList.stream()
                .flatMap(aeim -> aeim.getAeiList().stream())
                .map(AiopsExamIndex::getTagId)
                .collect(Collectors.toSet());

        ResponseBase tagData = restDmpUtil.getTagData(allTagIds);
        if (!tagData.checkIsSuccess()) {
            return ResponseBase.error(AIOPS_EXAM_TAG_DATA_ERROR);
        }

        List<Map<String, Object>> tagDataList = (List<Map<String, Object>>) tagData.getData();
        if (CollectionUtils.isEmpty(tagDataList)) {
            return ResponseBase.error(AIOPS_EXAM_TAG_DATA_ERROR);
        }
        Map<String, List<Map<String, Object>>> dimensionMap = tagDataList.stream().collect(Collectors.groupingBy(i -> {
            Map<String, Object> dimension = (Map<String, Object>) i.get("dimension");
            return Objects.toString(dimension.get("code"), "");
        }));

        Map<Long, String> tagDataMap = new HashMap<>();
        Map<Long, BigDecimal> tagDataScoreMap = new HashMap<>();
        for (Map.Entry<String, List<Map<String, Object>>> dMap : dimensionMap.entrySet()) {
            Map<String, List<Map<String, Object>>> valueTypeMap = dMap.getValue().stream()
                    .filter(a -> Objects.nonNull(a.get("valueType")))
                    .collect(Collectors.groupingBy(d -> StringUtil.toString(d.getOrDefault("valueType",""))));
            valueTypeMap.forEach((k, v) -> {
                List<Long> tagIdList = v.stream().map(i -> LongUtil.objectToLong(i.get("id"))).collect(Collectors.toList());
                // 查询标签数据
                selectTagDataAndTagScoreData(tagIdList, aiopsItemId, dMap.getKey(), k, tagDataMap, tagDataScoreMap);
            });
        }
        // 构建处理参数
        ScoreContext context = new ScoreContext(aeimList, aeitList, tagDataMap, tagDataScoreMap, aeiis.getId(), aiopsExamRecordMapper, aelsList, aerId
                , aeiis.getAiopsItem());
        TransactionStatus status = platformTransactionManager.getTransaction(TransactionDefinition.withDefaults());
        try {
            AiopsExamScoreAbstractHandler aeiisHandler = new AiopsExamInstanceIndexScoreHandler();
            AiopsExamScoreAbstractHandler aeiitsHandler = new AiopsExamInstanceIndexTypeScoreHandler();
            AiopsExamScoreAbstractHandler aeiInstanceScoreHandler = new AiopsExamItemInstanceScoreHandler();
            aeiisHandler.setSuccessor(aeiitsHandler);
            aeiitsHandler.setSuccessor(aeiInstanceScoreHandler);

            aeiisHandler.handleScore(context);

            // 更新体检记录为 体检中
            List<AiopsExamItemInstanceScore> aeiisList = aiopsExamRecordMapper.selectAiopsExamItemInstanceScore(aerId);
            if (aeiisList.stream().anyMatch(aeii -> !aeii.getExamComplete())) {
                updateAiopsExamRecordStatus(aerId, EXAM_ING, false);
            } else {
                updateAiopsExamRecordStatus(aerId, EXAM_COMPLETE, true);
            }
            platformTransactionManager.commit(status);
        } catch (Exception e) {
            if (!status.isCompleted()) {
                platformTransactionManager.rollback(status);
                log.error(" [computeScore]: error {}", e.getMessage(), e);
                //todo 体检状态 更新为失败
                aeiis.setExamInstanceStatus(AiopsExamItemInstanceScore.ExamInstanceStatus.EXAM_FAIL);
                aiopsExamRecordMapper.updateAiopsExamItemInstanceScoreById(aeiis);
                ResponseBase<Long> networkSecurityRb = networkSecurityExamService.getNetworkSecurityAe();
                if (networkSecurityRb.checkIsSuccess()) {
                    log.error(" [computeScore]:networkSecurity error {}", e.getMessage(), e);
                    Long networkSecurityExamRecordId = networkSecurityRb.getData();
                    aiopsExamRecordMapper.updateAiopsExamRecordStatus(networkSecurityExamRecordId, AiopsExamStatus.EXAM_FAIL, null);
                }
            }
            return ResponseBase.error(e);
        }
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase getAerReport(Long aerId, int scale) {
        AiopsExamRecord aer = aiopsExamRecordMapper.selectAiopsExamRecordReport(aerId);
        if (aer == null) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }

        List<AiopsExamItemInstanceScore> instanceScores = aiopsExamRecordMapper.selectAiopsExamItemInstanceScore(aerId);
        if (instanceScores.isEmpty()) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }

        AiopsExam exam = getAeById(aer.getAeId());
        if (exam == null) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }

        // 查询运维项目排序字段
        List<AiopsExamItemMap> aeimList = aiopsExamMapper.selectAiopsExamItemIndexByAeId(Lists.newArrayList(aer.getAeId()));
        Map<String, Long> aiopsItemMap = aeimList.stream().collect(Collectors.toMap(AiopsExamItemMap::getAiopsItem, AiopsExamItemMap::getId, (o, n) -> o));

        List<String> aiopsItemList = aiopsExamRecordMapper.selectAiopsExamItem(aerId).stream().map(AiopsExamItemMap::getAiopsItem)
                .collect(Collectors.toList());
        ;

        List<AiopsExamItemMap> enabledMaps = exam.getAeimList().stream()
                .filter(e -> aiopsItemList.contains(e.getAiopsItem()))
                .collect(Collectors.toList());

        Map<Long, AiopsExamIndex> indexMap = enabledMaps.stream()
                .flatMap(map -> map.getAeiList().stream())
                .collect(Collectors.toMap(AiopsExamIndex::getId, Function.identity()));

        Map<Long, AiopsExamItemInstanceScore> instanceScoreMap = instanceScores.stream()
                .collect(Collectors.toMap(AiopsExamItemInstanceScore::getId, Function.identity()));

        Map<Long, List<AiopsExamInstanceIndexTypeScore>> groupedScores = instanceScores.stream()
                .flatMap(score -> score.getAeiitsList().stream())
                .collect(Collectors.groupingBy(AiopsExamInstanceIndexTypeScore::getAeitId));

        Map<Long, List<AiopsExamInstanceIndexScore>> aeiInstanceMap = instanceScores.stream()
                .flatMap(score -> score.getAeInstanceIndexScoreList().stream())
                .collect(Collectors.groupingBy(AiopsExamInstanceIndexScore::getAeiId));

        Map<Long, AiopsExamItemIndexTypeScore> aeiitsMap = aer.getAeiitsList()
                .stream()
                .collect(Collectors.toMap(AiopsExamItemIndexTypeScore::getAeitId, Function.identity(), (o, n) -> o));

        Map<String, List<AiopsExamItemIndexScore>> aeiisMap = aer.getAeiIndexScoreList()
                .stream()
                .collect(Collectors.groupingBy(AiopsExamItemIndexScore::getAiopsItem));

        List<AiopsExamIndexType> indexTypes = aiopsExamMapper.selectIndexTypeByAeid(aer.getAeId());

        Map<Long, List<AiopsExamItemTypeScore>> typeScoreMap = aer.getAeitsList().stream()
                .collect(Collectors.groupingBy(AiopsExamItemTypeScore::getAeitId));

        processAeitList(indexTypes,
                aeiitsMap,
                groupedScores,
                instanceScoreMap,
                typeScoreMap,
                aeiisMap,
                indexMap,
                aiopsItemMap,
                aeiInstanceMap,
                scale);
        aer.setIndexTypeList(indexTypes);

        aer.setExamScore(aer.getExamScore().setScale(scale, RoundingMode.HALF_UP));
        aer.setLevelCode(getLevelCode(aelsList, itemInstanceScore.getExamScore()));
        return ResponseBase.ok(aer);
    }

    private void processAeitList(List<AiopsExamIndexType> aeitList,
                                 Map<Long, AiopsExamItemIndexTypeScore> aeitMap,
                                 Map<Long, List<AiopsExamInstanceIndexTypeScore>> aeitInstanceMap,
                                 Map<Long, AiopsExamItemInstanceScore> aeiInstanceIdMap,
                                 Map<Long, List<AiopsExamItemTypeScore>> aeitItemMap,
                                 Map<String, List<AiopsExamItemIndexScore>> aiopsItemIndexMap,
                                 Map<Long, AiopsExamIndex> aeiMap,
                                 Map<String, Long> aiopsItemMap,
                                 Map<Long, List<AiopsExamInstanceIndexScore>> aeiInstanceMap,
                                 int scale) {
        for (AiopsExamIndexType aeit : aeitList) {
            List<Long> aeiIdList = aeit.getAeiList().stream().map(AiopsExamIndex::getId).collect(Collectors.toList());

            AiopsExamItemIndexTypeScore aeiits = aeitMap.get(aeit.getId());

            if (aeiits == null) {
                log.error(" [processAeitList]: AiopsExamItemIndexTypeScore empty aeitId:{} , aeitCode:{}", aeit.getId(), aeit.getCode());
                continue;
            }
            // 分类分数四舍五入
            aeiits.setExamScore(aeiits.getExamScore().setScale(scale, RoundingMode.HALF_UP));
            // 分类下的实例
            List<AiopsExamInstanceIndexTypeScore> aeiitsLIst = aeitInstanceMap.get(aeit.getId());
            if (CollectionUtils.isEmpty(aeiitsLIst)) {
                log.error(" [processAeitList]: AiopsExamInstanceIndexTypeScore empty aeitId:{} , aeitCode:{}", aeit.getId(), aeit.getCode());
                continue;
            }

            aeit.setAeiriList(convert(aeiitsLIst));

            List<AiopsExamItemInstanceScore> aeiInstanceList = aeiitsLIst.stream()
                    .map(aeInstanceIndexType -> aeiInstanceIdMap.get(aeInstanceIndexType.getAeiisId()))
                    .collect(Collectors.toList());


            Map<String, List<AiopsExamItemInstanceScore>> typeItemInstanceMap = aeiInstanceList.stream()
                    .collect(Collectors.groupingBy(AiopsExamItemInstanceScore::getAiopsItem));
            List<AiopsExamItemTypeScore> subAeitsList = aeitItemMap.get(aeit.getId());

            List<AiopsExamItemIndexTypeScore.AiopsExamIndexTypeItem> aeitiList = subAeitsList.stream()
                    .map(subAeits -> {
                        AiopsExamItemIndexTypeScore.AiopsExamIndexTypeItem aeiti = new AiopsExamItemIndexTypeScore.AiopsExamIndexTypeItem();
                        aeiti.setAiopsItem(subAeits.getAiopsItem());
                        // 运维项目下分数四舍五入
                        aeiti.setExamScore(subAeits.getExamScore().setScale(scale, RoundingMode.HALF_UP));

                        // 获取运维项目下的实例分数
                        List<AiopsExamItemInstanceScore> subAeiisList = typeItemInstanceMap.get(aeiti.getAiopsItem());
                        aeiti.setAeiriList(convert(subAeiisList));

                        // 运维项目下 指标分类下 的指标
                        List<AiopsExamItemIndexScore> aeItemIndexScoreList = aiopsItemIndexMap.getOrDefault(aeiti.getAiopsItem(),new ArrayList<>())
                                .stream()
                                .filter(iis -> aeiIdList.contains(iis.getAeiId()))
                                .peek(i->i.setExamScore(i.getExamScore().setScale(scale, RoundingMode.HALF_UP)))
                                .collect(Collectors.toList());

                        // 设置每个指标下的实例
                        aeItemIndexScoreList.forEach(i -> {
                            i.setAei(aeiMap.get(i.getAeiId()));

                            // 获取指标相关的实例
                            List<AiopsExamInstanceIndexScore> aeiisList = aeiInstanceMap.get(i.getAeiId());
                            i.setAeiriList(convert(aeiisList));
                        });

                        aeiti.setAeItemIndexScoreList(aeItemIndexScoreList);
                        return aeiti;
                    })
                    .peek(i->i.setAeimId(aiopsItemMap.getOrDefault(i.getAiopsItem(),9999L)))
                    .sorted(Comparator.comparing(AiopsExamItemIndexTypeScore.AiopsExamIndexTypeItem::getAeimId))
                    .collect(Collectors.toList());

            aeiits.setAeitiList(aeitiList);
            aeit.setAeiits(aeiits);
            // 不需要显示
            aeit.setAeiList(null);
        }
    }

    private List<AiopsExamItemReportInstance> convert(List<? extends AiopsExamBase> aebList) {
        return aebList.stream().map(aeiis -> {
            AiopsExamItemReportInstance aeiit = new AiopsExamItemReportInstance();
            BeanUtils.copyProperties(aeiis, aeiit);
            return aeiit;
        }).collect(Collectors.toList());
    }

    @Override
    public AiopsExam getAeById(Long id) {

        ResponseBase<List<AiopsExam>> aiopsExamRb = getAiopsExam(id, null, null, false, 0, 0);
        if (CollectionUtils.isEmpty(aiopsExamRb.getData())) {
            // 无法计算分数
            return null;
        }
        return aiopsExamRb.getData().get(0);
    }

    private ResponseBase<AiopsExamRecord> getAerById(Long id) {
        // 获取体检记录详情
        List<AiopsExamRecord> aerList = aiopsExamMapper.selectAiopsExamRecord(new AiopsExamRecordParam(id));

        // 检查是否存在该记录
        if (CollectionUtils.isEmpty(aerList)) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }

        AiopsExamRecord aer = aerList.get(0);

        return ResponseBase.okT(aer);
    }

    private void selectTagDataAndTagScoreData(Collection<Long> tagIdList
            , String aiopsItemIdList, String dimension, String valueType, Map<Long, String> tagDataMap, Map<Long, BigDecimal> tagDataScoreMap) {

        String tagIdString = tagIdList.stream().map(LongUtil::safeToString).collect(Collectors.joining(","));

        String tagTable = (dimension + "_tag_" + valueType).toLowerCase();
        String tagScoreTable = (dimension + "_tag_score").toLowerCase();

        StringBuilder tagSql = new StringBuilder();
        StringBuilder tagScoreSql = new StringBuilder();


        tagSql.append("WITH RankedScores AS (")
                .append("SELECT *, ")
                .append("ROW_NUMBER() OVER (PARTITION BY id, tagId ORDER BY updateTime DESC) AS rn ")
                .append("FROM AIEOM.").append(tagTable)
                .append(" WHERE tagId IN (")
                .append(tagIdString)
                .append(") AND id IN ('")
                .append(aiopsItemIdList)
                .append("')")
                .append(" AND updateTime IS NOT NULL ")
                .append(")")
                .append("SELECT id, tagId, tagValue, updateTime ")
                .append("FROM RankedScores ")
                .append("WHERE rn = 1");

        tagScoreSql.append("WITH RankedScores AS (")
                .append("SELECT *, ")
                .append("ROW_NUMBER() OVER (PARTITION BY id, tagId ORDER BY updateTime DESC) AS rn ")
                .append("FROM AIEOM.").append(tagScoreTable)
                .append(" WHERE tagId IN (")
                .append(tagIdString)
                .append(") AND id IN ('")
                .append(aiopsItemIdList)
                .append("')")
                .append(" AND updateTime IS NOT NULL ")
                .append(")")
                .append("SELECT id, tagId, score, updateTime ")
                .append("FROM RankedScores ")
                .append("WHERE rn = 1");

        List<Map<String, Object>> tagDataList = bigDataUtil.srQuery(tagSql.toString());
        List<Map<String, Object>> tagDataScoreList = bigDataUtil.srQuery(tagScoreSql.toString());
        if (CollectionUtils.isEmpty(tagDataList) || CollectionUtils.isEmpty(tagDataScoreList)) {
            return;
        }

        for (Map<String, Object> tagData : tagDataList) {
            tagDataMap.put(LongUtil.objectToLong(tagData.get("tagId")), StringUtil.toString(tagData.get("tagValue")));
        }

        for (Map<String, Object> tagData : tagDataScoreList) {
            tagDataScoreMap.put(LongUtil.objectToLong(tagData.get("tagId")), BigDecimal.valueOf(DoubleUtil.objectToDouble(tagData.get("score"))));
        }

    }

    @Override
    public ResponseBase selectAiopsItemCount(AiopsExamRecord record) {
        record.setSid(RequestUtil.getHeaderSid());
        record.setEid(RequestUtil.getHeaderEid());
        List<AiopsExamItemInstanceScoreParam> aiopsExamItemInstanceScoreParams = aiopsExamRecordMapper.selectAiopsItemCount(record);
        List<AiopsExamItemInstanceScore> aiopsExamItemInstanceScores = aiopsExamRecordMapper.selectAiopsItemInstanceScore(record);
        Map<String, Object> map = new HashMap<>();
        map.put("aeiispList", aiopsExamItemInstanceScoreParams);
        map.put("aerList", aiopsExamItemInstanceScores);
        return ResponseBase.ok(map);
    }

    @Override
    public ResponseBase getRecentlyAiopsExamInstanceIndexTypeScore(AiopsExamRecord record) {
        record.setSid(RequestUtil.getHeaderSid());
        record.setEid(RequestUtil.getHeaderEid());
        List<AiopsExamRecord> AiopsExamRecord = aiopsExamRecordMapper.selectAiopsExamInstanceIndexTypeScore(record);
        // 分数不保留小数四舍五入
        AiopsExamRecord.stream().filter(Objects::nonNull).forEach(aer -> {
            // 总分
            aer.setExamScore(Objects.nonNull(aer.getExamScore()) ?
                    aer.getExamScore().setScale(SCALE, RoundingMode.HALF_UP) : null);
            // 实际值
            if (Objects.nonNull(aer.getAeiitsList())) {
                aer.getAeiitsList().stream().filter(Objects::nonNull).forEach(aeiits -> {
                    aeiits.setExamScore(Objects.nonNull(aeiits.getExamScore()) ?
                            aeiits.getExamScore().setScale(SCALE, RoundingMode.HALF_UP) : null);
                });
            }
        });
        return ResponseBase.ok(AiopsExamRecord);
    }

    public AiopsExamRecord getRecentlyAiopsExamRecord(Long eid){
        AiopsExamRecordParam param = new AiopsExamRecordParam();
        param.setEid(eid);
        param.setExamStatus(EXAM_COMPLETE.name());

        List<AiopsExamRecord> aerList = aiopsExamMapper.selectAiopsExamRecord(param)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aerList)) {
            return null;
        }
        return aerList.get(0);
    }
    @Override
    public void saveAiopsExamItemInstanceScore(AiopsExamItemInstanceScore score, Long recordId) {
        score.setId(SnowFlake.getInstance().newId());
        score.setAerId(recordId);
        score.setExamComplete(Boolean.FALSE);
        score.setExamInstanceStatus(AiopsExamItemInstanceScore.ExamInstanceStatus.NOT_START);
        aiopsExamRecordMapper.insertOrUpdateAiopsExamItemInstanceScore(score);
    }

    @Override
    public void deleteAiopsExamItemInstanceScore(Long recordId) {
        aiopsExamRecordMapper.deleteExamInstance(recordId);
    }

    @Override
    public ResponseBase aiopsIndexSave(AiopsExamIndex aei) {
        if (LongUtil.isEmpty(aei.getId())) {
            aei.setId(SnowFlake.getInstance().newId());
        }
        aiopsExamRecordMapper.insertOrUpdateAei(aei);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase aiopsIndexDelete(Long aeiId) {
        aiopsExamRecordMapper.deleteAei(aeiId);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase aiopsIndexTagMapping(Long aeiId, Long tagId) {
        aiopsExamRecordMapper.aiopsIndexTagMapping(aeiId, tagId);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase aiopsExamItemEnable(Long aeimId, Boolean enable) {
        aiopsExamRecordMapper.aiopsExamItemEnable(aeimId, enable);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase aiopsIndexGet(Long aeiId) {
        return ResponseBase.ok(aiopsExamRecordMapper.selectAeiById(aeiId));
    }

    @Override
    public ResponseBase insertAiopsExam(AiopsExam aiopsExam) {
        aiopsExam.setId(SnowFlake.getInstance().newId());
        aiopsExam.setCreateDate(LocalDateTime.now());
        aiopsExam.setUpdateDate(LocalDateTime.now());
        if (ObjectUtils.isEmpty(aiopsExam.getCode()) || ObjectUtils.isEmpty(aiopsExam.getName())) {
            return ResponseBase.error(AIOPS_EXAM_NULL);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("code", aiopsExam.getCode());
        map.put("name", aiopsExam.getName());
        if (!CollectionUtils.isEmpty(aiopsExamMapper.selectAiopsExamListByCode(map))) {
            return ResponseBase.error(AIOPS_EXAM_CODE_IS_REPEAT);
        }
        if (!CollectionUtils.isEmpty(aiopsExamMapper.selectAiopsExamListByName(map))) {
            return ResponseBase.error(AIOPS_EXAM_NAME_IS_REPEAT);
        }
        aiopsExamMapper.insertAiopsExam(aiopsExam);
        return ResponseBase.ok(aiopsExam.getId());
    }

    @Override
    public ResponseBase<List<AiopsExam>> selectAiopsExamList(Long id, String codeOrName) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("codeOrName", codeOrName);
        List<AiopsExam> aiopsExamList = aiopsExamMapper.selectAiopsExamList(map);
        if (CollectionUtils.isEmpty(aiopsExamList)) {
            return ResponseBase.ok();
        }
        List<Long> aeIdList = aiopsExamList.stream().map(AiopsExam::getId).collect(Collectors.toList());
        List<AiopsExamItemMap> aiopsExamItemMapList = aiopsExamMapper.selectAiopsExamItemIndexList(aeIdList);
        Map<Long, List<AiopsExamItemMap>> aeMap = aiopsExamItemMapList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeId));

        // 查詢出所有體檢項分類
        List<AiopsExamItemType> aeitList = aiopsExamMapper.selectAiopsExamItemTypeByAeId(aeIdList);
        Map<Long, List<AiopsExamItemType>> aeitCollectors = aeitList.stream().collect(Collectors.groupingBy(AiopsExamItemType::getAeId));

        List<String> aiopsItemList = aiopsExamItemMapList.stream()
                .map(AiopsExamItemMap::getAiopsItem)
                .collect(Collectors.toList());
        Map<String, String> deviceMap = aiopsExamMapper.selectAiopsItemTotal(aiopsItemList).stream()
                .collect(Collectors.toMap(s -> s.get("aiopsItem"),
                        s -> String.valueOf(s.getOrDefault("deviceCount","0")), (x, y) -> x));

        for (AiopsExam aiopsExam : aiopsExamList) {
            List<AiopsExamItemMap> aeimList = aeMap.getOrDefault(aiopsExam.getId(),new ArrayList<>());
            aeimList.forEach(aeim -> {
                aeim.setAeId(aiopsExam.getId());
                String deviceCount = deviceMap.get(aeim.getAiopsItem());
                if (StringUtils.isNotEmpty(deviceCount)) {
                    aeim.setDeviceCount(Long.valueOf(deviceCount));
                }
            });
            Map<Long, List<AiopsExamItemMap>> aeitMap = aeimList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeitId));
            List<AiopsExamItemType> aeitCollectorsList = aeitCollectors.getOrDefault(aiopsExam.getId(),new ArrayList<>());
            aeitCollectorsList.forEach(i -> i.setAeimList(aeitMap.getOrDefault(i.getId(),new ArrayList<>())));
            buildHierarchy(aeitCollectorsList);
            aiopsExam.setAeitList(aeitCollectorsList);
            aiopsExam.setAeimList(aeimList);
        }
        return ResponseBase.ok(aiopsExamList);
    }

    @Override
    public ResponseBase insertAiopsExamItemType(AiopsExamItemType aiopsExamItemType) {
        if (aiopsExamItemType.getCode() == null || aiopsExamItemType.getCode().equals("")) {
            return ResponseBase.error(CODE_IS_NULL);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", aiopsExamItemType.getCode());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemTypeByCode(map))){
            return ResponseBase.error(AIOPS_EXAM_ITEM_TYPE_CODE_IS_REPEAT);
        }
        if (aiopsExamItemType.getName() == null || aiopsExamItemType.getName().equals("")) {
            return ResponseBase.error(NAME_IS_NULL);
        }
        map.put("name", aiopsExamItemType.getName());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemTypeByName(map))){
            return ResponseBase.error(AIOPS_EXAM_ITEM_TYPE_NAME_IS_REPEAT);
        }
        aiopsExamItemType.setId(SnowFlake.getInstance().newId());
        aiopsExamItemType.setCreateDate(LocalDateTime.now());
        aiopsExamItemType.setUpdateDate(LocalDateTime.now());
        aiopsExamMapper.insertAiopsExamItemType(aiopsExamItemType);
        return ResponseBase.ok(aiopsExamItemType.getId());
    }

    @Override
    public ResponseBase insertAiopsExamItemMap(AiopsExamItemMap aiopsExamItemMap) {
        String[] splitAiopsItem = aiopsExamItemMap.getAiopsItem().split(",");
        List<Object> list = new ArrayList<>();
        for (String aiopsItem : splitAiopsItem) {
            AiopsExamItemMap examItemMap = new AiopsExamItemMap();
            long id = SnowFlake.getInstance().newId();
            list.add(id);
            examItemMap.setId(id);
            examItemMap.setAeId(aiopsExamItemMap.getAeId());
            examItemMap.setAeitId(aiopsExamItemMap.getAeitId());
            examItemMap.setAiopsItem(aiopsItem);
            examItemMap.setEnabled(aiopsExamItemMap.getEnabled());
            examItemMap.setCreateDate(LocalDateTime.now());
            examItemMap.setUpdateDate(LocalDateTime.now());
            if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemMapByAeimId(examItemMap))) {
                return ResponseBase.error(AIOPS_EXAM_ITEM_AIOPS_ITEM_IS_REPEAT);
            }
            aiopsExamMapper.insertAiopsExamItemMap(examItemMap);
        }
        return ResponseBase.ok(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase updateAiopsExamItemMap(AiopsExamItemMap aiopsExamItemMap) {
        aiopsExamItemMap.setUpdateDate(LocalDateTime.now());
        return ResponseBase.ok(aiopsExamMapper.updateAiopsExamItemMap(aiopsExamItemMap));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteAiopsExamItemMap(Long aeimId) {
        // 体检项目下有指标不可以删除
        if (!CollectionUtils.isEmpty(aiopsExamMapper.selectAiopsExamIndexByAeimId(aeimId))) {
            return ResponseBase.error(AIOPS_EXAM_INDEX_IS_EXISTS);
        }
        return ResponseBase.ok(aiopsExamMapper.deleteAiopsExamItemMap(aeimId));
    }

    @Override
    public ResponseBase<List<AiopsExam>> selectAiopsExamItemMap(Long id) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", id);
            List<AiopsExam> aiopsExamList = aiopsExamMapper.selectAiopsExamList(map);
            if (CollectionUtils.isEmpty(aiopsExamList)) {
                return ResponseBase.ok();
            }
            List<Long> aeIdList = aiopsExamList.stream().map(AiopsExam::getId).collect(Collectors.toList());
            // 组装数据 拼接体检项目类型
            List<AiopsExamItemType> aeitList = aiopsExamMapper.selectAiopsExamItemTypeByAeId(aeIdList);
            // 组装数据 拼接运维项目
            List<AiopsExamItemMap> aiopsExamItemMapList = aiopsExamMapper.selectAiopsExamItemMap(aeIdList);

            Map<Long, List<AiopsExamItemMap>> aeMap = aiopsExamItemMapList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeId));
            for (AiopsExam aiopsExam : aiopsExamList) {
                List<AiopsExamItemMap> aeimList = aeMap.getOrDefault(aiopsExam.getId(),new ArrayList<>());
                Map<Long, List<AiopsExamItemMap>> aeitMap = aeimList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeitId));
                aeitList.forEach(i -> i.setAeimList(aeitMap.getOrDefault(i.getId(),new ArrayList<>())));
                buildHierarchy(aeitList);
                aiopsExam.setAeitList(aeitList);
                // 后端计算分数使用
                aiopsExam.setAeimList(aiopsExamItemMapList);
            }
            return ResponseBase.okT(aiopsExamList);
        }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteAiopsExamItemType(Long id) {
        // 分类有子级不可删除
        Map<String, Object> parentIdMap = new HashMap<>();
        parentIdMap.put("parentId", id);
        if (!CollectionUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemType(parentIdMap))) {
            return ResponseBase.error(AIOPS_EXAM_ITEM_TYPE_PARENT_IS_EXISTS);
        }
        Map<String, Object> aeitIdMap = new HashMap<>();
        aeitIdMap.put("aeitId",id);
        if (!CollectionUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemMapById(aeitIdMap))) {
            return ResponseBase.error(AIOPS_EXAM_ITEM_MAP_IS_EXISTS);
        }
        return ResponseBase.ok(aiopsExamMapper.deleteAiopsExamItemType(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase updateAiopsExamItemType(AiopsExamItemType aiopsExamItemType) {
        aiopsExamItemType.setUpdateDate(LocalDateTime.now());
        if (aiopsExamItemType.getCode() == null || aiopsExamItemType.getCode().equals("")) {
            return ResponseBase.error(CODE_IS_NULL);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", aiopsExamItemType.getCode());
        map.put("id", aiopsExamItemType.getId());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemTypeByCode(map))){
            return ResponseBase.error(AIOPS_EXAM_ITEM_TYPE_CODE_IS_REPEAT);
        }
        if (aiopsExamItemType.getName() == null || aiopsExamItemType.getName().equals("")) {
            return ResponseBase.error(NAME_IS_NULL);
        }
        map.put("name", aiopsExamItemType.getName());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamItemTypeByName(map))){
            return ResponseBase.error(AIOPS_EXAM_ITEM_TYPE_NAME_IS_REPEAT);
        }
        return ResponseBase.ok(aiopsExamMapper.updateAiopsExamItemType(aiopsExamItemType));
    }

    @Override
    public ResponseBase insertAiopsExamIndex(AiopsExamIndex aiopsExamIndex) {
        if (aiopsExamIndex.getCode() == null || aiopsExamIndex.getCode().equals("")) {
            return ResponseBase.error(CODE_IS_NULL);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", aiopsExamIndex.getCode());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamIndexByCode(map))){
            return ResponseBase.error(AIOPS_EXAM_INDEX_CODE_IS_REPEAT);
        }
        if (aiopsExamIndex.getName() == null || aiopsExamIndex.getName().equals("")) {
            return ResponseBase.error(NAME_IS_NULL);
        }
        map.put("name", aiopsExamIndex.getName());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamIndexByName(map))){
            return ResponseBase.error(AIOPS_EXAM_INDEX_NAME_IS_REPEAT);
        }
        aiopsExamIndex.setId(SnowFlake.getInstance().newId());
        aiopsExamIndex.setCreateDate(LocalDateTime.now());
        aiopsExamIndex.setUpdateDate(LocalDateTime.now());
        aiopsExamMapper.insertAiopsExamIndex(aiopsExamIndex);
        return ResponseBase.ok(aiopsExamIndex.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteAiopsExamIndex(Long id) {
        Integer deleteStatus = aiopsExamMapper.deleteAiopsExamIndex(id);
        return ResponseBase.ok(deleteStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase updateAiopsExamIndexType(List<AiopsExamIndexType> aiopsExamIndexTypes) {
        ArrayList<Object> list = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (AiopsExamIndexType aiopsExamIndexType : aiopsExamIndexTypes) {
            if (aiopsExamIndexType.getCode() == null || aiopsExamIndexType.getCode().equals("")) {
                throw new RuntimeException("code is null");
            }
            AiopsExamIndexType aiopsExamIndexTypeCode = aiopsExamMapper.selectAiopsExamIndexTypeByCode(aiopsExamIndexType.getCode());
            if (!ObjectUtils.isEmpty(aiopsExamIndexTypeCode) &&
                    (aiopsExamIndexType.getId() == null || !aiopsExamIndexTypeCode.getId().equals(aiopsExamIndexType.getId()))) {
                return ResponseBase.error(PROFILES_TEMPLATE_NUM_EXISTS);
            }
            AiopsExamIndexType aiopsExamIndexTypeName = aiopsExamMapper.selectAiopsExamIndexTypeByName(aiopsExamIndexType.getName());
            if (!ObjectUtils.isEmpty(aiopsExamIndexTypeName) &&
                    (aiopsExamIndexType.getId() == null || !aiopsExamIndexTypeName.getId().equals(aiopsExamIndexType.getId()))) {
                return ResponseBase.error(PROFILES_TEMPLATE_NAME_EXISTS);
            }
            if (aiopsExamIndexType.getId() != null && aiopsExamIndexType.getId() > 0) {
                // 更新
                aiopsExamIndexType.setUpdateDate(now);
                list.add(aiopsExamMapper.updateAiopsExamIndexType(aiopsExamIndexType));
            } else {
                // 新增
                aiopsExamIndexType.setId(SnowFlake.getInstance().newId());
                aiopsExamIndexType.setCreateDate(now);
                aiopsExamIndexType.setUpdateDate(now);
                aiopsExamMapper.insertAiopsExamIndexType(aiopsExamIndexType);
                list.add(aiopsExamIndexType.getId());
            }
        }
        return ResponseBase.ok(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteAiopsExamIndexType(String id) {
        boolean status = true;
        String[] split = id.split(",");
        Map<String, Object> map = new HashMap<>();
        for (String splitId : split){
            // 指標分類下有指标不可以删除
            map.put("aeitId", splitId);
            if (!CollectionUtils.isEmpty(aiopsExamMapper.selectAiopsExamIndexByAeitId(map))) {
                status = false;
                break;
            }
        }
        if (status) {
            int delete = 0;
            for (String splitId : split) {
                delete = aiopsExamMapper.deleteAiopsExamIndexType(Long.parseLong(splitId));
            }
            return ResponseBase.ok(delete);
        } else {
            return ResponseBase.error(AIOPS_EXAM_INDEX_IS_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase updateAiopsExamIndex(AiopsExamIndex aiopsExamIndex) {
        if (aiopsExamIndex.getCode() == null || aiopsExamIndex.getCode().equals("")) {
            return ResponseBase.error(CODE_IS_NULL);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", aiopsExamIndex.getCode());
        map.put("id", aiopsExamIndex.getId());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamIndexByCode(map))){
            return ResponseBase.error(AIOPS_EXAM_INDEX_CODE_IS_REPEAT);
        }
        if (aiopsExamIndex.getName() == null || aiopsExamIndex.getName().equals("")) {
            return ResponseBase.error(NAME_IS_NULL);
        }
        map.put("name",aiopsExamIndex.getName());
        if (!ObjectUtils.isEmpty(aiopsExamMapper.selectAiopsExamIndexByName(map))){
            return ResponseBase.error(AIOPS_EXAM_INDEX_NAME_IS_REPEAT);
        }
        aiopsExamIndex.setUpdateDate(LocalDateTime.now());
        return ResponseBase.ok(aiopsExamMapper.updateAiopsExamIndex(aiopsExamIndex));
    }

    @Override
    public ResponseBase<List<AiopsExamIndex>> selectExamIndex(Long aeitId, Long aeId, Long aeimId, String codeOrName) {
        Map<String, Object> map = new HashMap<>();
        map.put("aeitId", aeitId);
        map.put("aeId", aeId);
        map.put("codeOrName", codeOrName);
        map.put("aeimId", aeimId);
        List<AiopsExamIndex> aiopsExamIndexList = aiopsExamMapper.selectExamIndex(map);
        return ResponseBase.ok(aiopsExamIndexList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase updateAiopsExamIndexTag(AiopsExamIndex aiopsExamIndex) {
        aiopsExamIndex.setUpdateDate(LocalDateTime.now());
        return ResponseBase.ok(aiopsExamMapper.updateAiopsExamIndex(aiopsExamIndex));
    }

    @Override
    public ResponseBase selectAiopsExamIndexList(Long id, Long aeimId, Long aeitId, String codeOrName, int pageNum, int pageSize) {
        Map<String, Object> map = new HashMap<>();
        map.put("aeId", id);
        map.put("aeimId", aeimId);
        map.put("aeitId", aeitId);
        map.put("codeOrName", codeOrName);
        Page page = PageHelper.startPage(pageNum, pageSize);
        aiopsExamMapper.selectAiopsExamIndexList(map);
        PageInfo<AiopsExam> pageInfo = new PageInfo<>(page);
        return ResponseBase.ok(pageInfo);
    }

    @Override
    public ResponseBase selectAiopsExamIndexType(Long id, Long aeitId) {
        Map<String, Object> map = new HashMap<>();
        map.put("aeId", id);
        map.put("id",aeitId);
        return ResponseBase.ok(aiopsExamMapper.selectAiopsExamIndexType(map));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase updateAiopsExamIndexWeight(List<AiopsExamIndex> aiopsExamIndexList) {
        aiopsExamIndexList.forEach(e->e.setUpdateDate(LocalDateTime.now()));
        return ResponseBase.ok(aiopsExamMapper.updateAiopsExamIndexWeight(aiopsExamIndexList));
    }

    @Override
    public ResponseBase<List<AiopsExam>> selectAiopsExam(Long id, String codeOrName) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("codeOrName", codeOrName);
        List<AiopsExam> aiopsExamList = aiopsExamMapper.selectAiopsExamList(map);
        if (CollectionUtils.isEmpty(aiopsExamList)) {
            return ResponseBase.ok();
        }
        List<Long> aeIdList = aiopsExamList.stream().map(AiopsExam::getId).collect(Collectors.toList());
        List<AiopsExamItemMap> aiopsExamItemMapList = aiopsExamMapper.selectAiopsExamItemMapList(aeIdList);
        Map<Long, List<AiopsExamItemMap>> aeMap = aiopsExamItemMapList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeId));

        // 查詢出所有體檢項分類
        List<AiopsExamItemType> aeitList = aiopsExamMapper.selectAiopsExamItemTypeByAeId(aeIdList);
        Map<Long, List<AiopsExamItemType>> aeitCollectors = aeitList.stream().collect(Collectors.groupingBy(AiopsExamItemType::getAeId));

        for (AiopsExam aiopsExam : aiopsExamList) {
            List<AiopsExamItemMap> aeimList = aeMap.getOrDefault(aiopsExam.getId(),new ArrayList<>());
            aeimList.forEach(aeim -> aeim.setAeId(aiopsExam.getId()));
            Map<Long, List<AiopsExamItemMap>> aeitMap = aeimList.stream().collect(Collectors.groupingBy(AiopsExamItemMap::getAeitId));
            List<AiopsExamItemType> aeitCollectorsList = aeitCollectors.getOrDefault(aiopsExam.getId(),new ArrayList<>());
            aeitCollectorsList.forEach(i -> i.setAeimList(aeitMap.getOrDefault(i.getId(),new ArrayList<>())));
            buildHierarchy(aeitCollectorsList);
            aiopsExam.setAeitList(aeitCollectorsList);
            aiopsExam.setAeimList(aeimList);
            aiopsExam.getAeitList().sort(Comparator.comparing(AiopsExamItemType::getId));
            for (AiopsExamItemType itemType : aiopsExam.getAeitList()) {
                if (itemType.getAeimList() != null) {
                    itemType.getAeimList().sort(Comparator.comparing(AiopsExamItemMap::getId));
                }
            }
        }
        return ResponseBase.okT(aiopsExamList);
    }

    @Override
    public ResponseBase deleteExamRecord(Long id) {
        int result = aiopsExamMapper.deleteExamRecordById(id);
        if (result > 0) {
            return ResponseBase.ok();
        } else {
            return ResponseBase.error(AIOPS_EXAM_ERROR, "删除失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase reExamRecord(Long recordId) {
        List<AiopsExamItemInstanceScore> instanceScoreList = aiopsExamRecordMapper.
                selectScoresByAerIdListOrNetworkExamCategoryCode(Collections.singletonList(recordId), null);

        if (CollectionUtils.isEmpty(instanceScoreList)) {
            return ResponseBase.error(AIOPS_EXAM_ERROR, "not found instance");
        }

        List<Long> instanceIdList = instanceScoreList.stream().map(AiopsExamItemInstanceScore::getId).collect(Collectors.toList());
        aiopsExamRecordMapper.deleteComputeRecord(recordId);
        aiopsExamRecordMapper.deleteComputeInstance(instanceIdList);
        for (AiopsExamItemInstanceScore instanceScore : instanceScoreList) {
            AiopsExamItemInstanceScore newInstanceScore = new AiopsExamItemInstanceScore();
            newInstanceScore.setAiopsItem(instanceScore.getAiopsItem());
            newInstanceScore.setNetworkExamCategoryCode(instanceScore.getNetworkExamCategoryCode());
            newInstanceScore.setAiopsItemId(instanceScore.getAiopsItemId());
            newInstanceScore.setNetworkExamAssetId(instanceScore.getNetworkExamAssetId());
            saveAiopsExamItemInstanceScore(newInstanceScore,recordId);

        }
        aiopsExamRecordMapper.updateAiopsExamRecordStatus(recordId, NOT_START,null);
        return ResponseBase.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse<AiopsExamRecordsReportRecord> insertAiopsExamRecordsReportRecord(AiopsExamRecordsReportRecord reportRecord) {

        Optional<BaseResponse> optResponse = checkParamIsEmpty(reportRecord.getAerId(), "aerId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(reportRecord.getReportType(), "reportType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        try {
            // 删除旧报告
            deleteOldReports(reportRecord.getAerId(),reportRecord.getReportType());
            
            // 设置新报告基本信息
            initReportRecord(reportRecord);
            
            // 插入新报告
            int result = aiopsExamRecordMapper.insertAiopsExamRecordsReportRecord(reportRecord);
            if (result <= 0) {
                // todo
                return BaseResponse.error(INTERNAL_ERROR,"[insertAiopsExamRecordsReportRecord] insert error");
            }
            
            return BaseResponse.okT(reportRecord);
            
        } catch (Exception e) {
            log.error("[insertAiopsExamRecordsReportRecord] error", e);
            return BaseResponse.error(INTERNAL_ERROR);
        }
    }
    
    /**
     * 删除旧的报告记录
     */
    private void deleteOldReports(Long aerId, String reportType) {
        List<Long> reportIdList = aiopsExamRecordMapper.selectAiopsExamRecordsReportRecordByAerId(aerId);
        if (!CollectionUtils.isEmpty(reportIdList)) {
            aiopsExamRecordMapper.deleteAiopsExamRecordsReportRecordByAerId(aerId);
            reportIdList.forEach(id -> {
                if (NetworkReportType.Simple.name().equals(reportType)) {
                    networkSecurityExamSimpleEsReport.deleteReport(String.valueOf(id));
                } else {
                    networkSecurityExamEsReport.deleteReport(String.valueOf(id));
                }
            });
        }
    }
    
    /**
     * 初始化报告记录基本信息
     */
    private void initReportRecord(AiopsExamRecordsReportRecord reportRecord) {
        reportRecord.setId(SnowFlake.getInstance().newId());
        reportRecord.setReportDate(Optional.ofNullable(reportRecord.getReportDate())
                .orElse(LocalDate.now()));
        reportRecord.setReportStatus(ReportStatus.GENERATING.getIndex());
        reportRecord.setSid(RequestUtil.getHeaderSid());
    }
}
